#!/bin/bash
set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 错误退出函数
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# 成功信息输出
success_msg() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 信息输出
info_msg() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

# 警告信息输出
warn_msg() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

### PHASE 1: LOCAL INFRASTRUCTURE DEPLOYMENT ###
echo -e "${GREEN}===========================================${NC}"
echo -e "${GREEN}    Phase 1: Local Infrastructure Setup${NC}"
echo -e "${GREEN}===========================================${NC}"

# 1. 请确保pulumi以及依赖的python包已经安装
# 2. 把pulumi需要的物料都放在infrastructure目录下
# 3. 执行pulumi up
info_msg "检查infrastructure目录..."
if [ ! -d "infrastructure" ]; then
  mkdir infrastructure
fi

if [ "$(ls -A infrastructure)" = "" ]; then
  error_exit "infrastructure目录为空,请确保pulumi物料已经正确放在infrastructure目录下"
fi

info_msg "开始执行pulumi部署..."
export AWS_PROFILE=newaws
pulumi stack select test
pulumi up

success_msg "Pulumi部署完成!"

### PHASE 2: REMOTE MACHINE SETUP ###
echo ""
echo -e "${GREEN}===========================================${NC}"
echo -e "${GREEN}    Phase 2: Remote Machine Deployment${NC}"
echo -e "${GREEN}===========================================${NC}"

# 提示用户输入远程机器地址
echo ""
echo -e "${YELLOW}请输入远程机器的主机名或IP地址:${NC}"
read -p "Remote hostname/IP: " REMOTE_HOST

if [ -z "$REMOTE_HOST" ]; then
    error_exit "远程主机地址不能为空"
fi

echo ""
echo -e "${YELLOW}请输入远程机器的SSH用户名 (默认: ubuntu):${NC}"
read -p "SSH username [ubuntu]: " REMOTE_USER
REMOTE_USER=${REMOTE_USER:-ubuntu}

echo ""
echo -e "${YELLOW}请输入SSH私钥文件路径 (留空使用默认SSH配置):${NC}"
read -p "SSH key path [default]: " SSH_KEY_PATH

# 构建SSH命令
SSH_CMD="ssh"
SCP_CMD="scp"
if [ -n "$SSH_KEY_PATH" ]; then
    SSH_CMD="ssh -i $SSH_KEY_PATH"
    SCP_CMD="scp -i $SSH_KEY_PATH"
fi

info_msg "测试SSH连接到 $REMOTE_USER@$REMOTE_HOST..."
if ! $SSH_CMD -o ConnectTimeout=10 -o BatchMode=yes $REMOTE_USER@$REMOTE_HOST "echo 'SSH连接成功'" 2>/dev/null; then
    error_exit "无法连接到远程主机 $REMOTE_USER@$REMOTE_HOST，请检查网络连接、SSH配置和认证"
fi

success_msg "SSH连接测试成功!"

# 创建远程部署脚本
info_msg "创建远程部署脚本..."
cat > /tmp/remote_deploy.sh << 'EOF'
#!/bin/bash
set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m'

error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

success_msg() {
    echo -e "${GREEN}✅ $1${NC}"
}

info_msg() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn_msg() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

cd /tmp/deployment

### galaxy ###
echo -e "${GREEN}===========================================${NC}"
echo -e "${GREEN}        Galaxy Deployment${NC}"
echo -e "${GREEN}===========================================${NC}"

## 数据库导入 TODO

## 部署galaxy
info_msg "检查galaxy目录..."
if [ ! -d "galaxy" ]; then
  mkdir galaxy
fi

if [ "$(ls -A galaxy)" = "" ]; then
  error_exit "galaxy目录为空,请确保galaxy物料已经正确放在galaxy目录下"
fi

info_msg "开始部署galaxy..."
cd galaxy
if [ -f "deploy_all.sh" ]; then
    chmod +x deploy_all.sh
    ./deploy_all.sh
else
    error_exit "deploy_all.sh 文件不存在"
fi
cd ..

success_msg "Galaxy部署完成!"

### apps ###
echo ""
echo -e "${GREEN}===========================================${NC}"
echo -e "${GREEN}        Apps Deployment${NC}"
echo -e "${GREEN}===========================================${NC}"

## 导入业务数据 TODO
warn_msg "业务数据导入功能待实现"

## Oracle 导入 TODO
warn_msg "Oracle导入功能待实现"

success_msg "所有远程部署任务完成!"
EOF

chmod +x /tmp/remote_deploy.sh

# 准备要传输的文件和目录
info_msg "准备传输文件到远程主机..."

# 创建临时目录用于打包
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# 复制需要的文件到临时目录
cp -r galaxy "$TEMP_DIR/" 2>/dev/null || warn_msg "galaxy目录不存在，将在远程创建空目录"
cp -r apps "$TEMP_DIR/" 2>/dev/null || warn_msg "apps目录不存在，将在远程创建空目录"

# 如果目录不存在，创建空目录
[ ! -d "$TEMP_DIR/galaxy" ] && mkdir -p "$TEMP_DIR/galaxy"
[ ! -d "$TEMP_DIR/apps" ] && mkdir -p "$TEMP_DIR/apps"

# 创建部署包
info_msg "创建部署包..."
cd "$TEMP_DIR"
tar -czf deployment.tar.gz galaxy apps
cd - > /dev/null

# 传输文件到远程主机
info_msg "传输文件到远程主机 $REMOTE_USER@$REMOTE_HOST..."

# 在远程主机创建目录
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "mkdir -p /tmp/deployment"

# 传输部署包
$SCP_CMD "$TEMP_DIR/deployment.tar.gz" $REMOTE_USER@$REMOTE_HOST:/tmp/deployment/

# 传输远程部署脚本
$SCP_CMD /tmp/remote_deploy.sh $REMOTE_USER@$REMOTE_HOST:/tmp/deployment/

# 在远程主机解压文件
info_msg "在远程主机解压文件..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd /tmp/deployment && tar -xzf deployment.tar.gz"

success_msg "文件传输完成!"

# 执行远程部署
echo ""
echo -e "${GREEN}===========================================${NC}"
echo -e "${GREEN}    开始远程部署执行${NC}"
echo -e "${GREEN}===========================================${NC}"

info_msg "在远程主机 $REMOTE_USER@$REMOTE_HOST 上执行部署脚本..."

# 执行远程部署脚本
if $SSH_CMD $REMOTE_USER@$REMOTE_HOST "cd /tmp/deployment && chmod +x remote_deploy.sh && ./remote_deploy.sh"; then
    echo ""
    echo -e "${GREEN}===========================================${NC}"
    echo -e "${GREEN}🎉 所有部署任务完成！${NC}"
    echo -e "${GREEN}===========================================${NC}"
    echo ""
    success_msg "本地基础设施部署: ✅ 完成"
    success_msg "远程应用部署: ✅ 完成"
    echo ""
    info_msg "部署摘要:"
    echo "  - 本地执行: Pulumi基础设施部署"
    echo "  - 远程主机: $REMOTE_USER@$REMOTE_HOST"
    echo "  - 远程执行: Galaxy和Apps部署"
    echo ""
else
    error_exit "远程部署执行失败"
fi

# 清理临时文件
info_msg "清理临时文件..."
$SSH_CMD $REMOTE_USER@$REMOTE_HOST "rm -rf /tmp/deployment" 2>/dev/null || warn_msg "清理远程临时文件失败"
rm -f /tmp/remote_deploy.sh

success_msg "部署流程全部完成!"



